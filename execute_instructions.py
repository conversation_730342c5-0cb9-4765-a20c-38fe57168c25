#!/usr/bin/env python3
import json
import sys
import time
import logging
import requests
import argparse

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def execute_http_request(instruction, mode="text_infer_v3"):
    """Execute an HTTP request with the given instruction."""
    start_time = time.time()
    headers = {"Content-Type": "application/json"}

    # Configure different settings based on mode
    if mode == "text_infer_v3":
        url = "http://mmfinderdrsandboxfdsvr.production.polaris/v1/agent/run_headless"
        data = {
            "app_id": "wx25f982a55e60a540",
            "run_mode": "text_infer_v3",
            "from_username": "wxid_adldhsbu7ip312",
            "request_id": f"test_session_id_{int(time.time())}",
            "headless_mode": "2",
            "uin": "3194644195",
            "username": "wxid_adldhsbu7ip312",
            "instruction": instruction
        }
    elif mode == "text_infer_xml":
        url = "http://************/v1/agent/run_headless"
        data = {
            "app_id": "wx25f982a55e60a540",
            "model_name": "eval_jhinzhou-Qwen3-32B-0609-10",
            "base_url": "http://llmproxy-offline.lubanllm.polaris:9000/v1/test/",
            "run_mode": "text_infer_xml",
            "from_username": "wxid_7tzy6unka3lm12",
            "request_id": "test_session_id_aaa",
            "headless_mode": "2",
            "uin": "3193644812",
            "username": "wxid_7tzy6unka3lm12",
            "instruction": instruction
        }
    else:
        raise ValueError(f"Unsupported mode: {mode}. Supported modes: text_infer_v3, text_infer_xml")

    logger.info(f"Executing instruction: {instruction}")

    try:
        # Execute the HTTP request using requests
        response = requests.post(
            url,
            json=data,
            headers=headers,
            timeout=3600  # 1 hour timeout
        )
        execution_time = time.time() - start_time

        # Check if request was successful
        response.raise_for_status()

        response_text = response.text
        logger.info("Request executed successfully")
        logger.info(f"Response: {response_text[:200]}...")  # Show first 200 chars

        return {
            "success": True,
            "response": response_text,
            "execution_time": execution_time,
            "status_code": response.status_code
        }

    except requests.exceptions.Timeout:
        execution_time = time.time() - start_time
        error_msg = "Request timed out after 1 hour"
        logger.error(error_msg)
        return {
            "success": False,
            "error": error_msg,
            "execution_time": execution_time
        }
    except requests.exceptions.ConnectionError:
        execution_time = time.time() - start_time
        error_msg = "Connection error - unable to reach the server"
        logger.error(error_msg)
        return {
            "success": False,
            "error": error_msg,
            "execution_time": execution_time
        }
    except requests.exceptions.HTTPError as e:
        execution_time = time.time() - start_time
        error_msg = f"HTTP error {e.response.status_code}: {e.response.text}"
        logger.error(error_msg)
        return {
            "success": False,
            "error": error_msg,
            "execution_time": execution_time,
            "status_code": e.response.status_code
        }
    except Exception as e:
        execution_time = time.time() - start_time
        error_msg = f"Unexpected error: {str(e)}"
        logger.error(error_msg)
        return {
            "success": False,
            "error": error_msg,
            "execution_time": execution_time
        }

def main():
    parser = argparse.ArgumentParser(description='Execute instructions from JSON file')
    parser.add_argument('json_file', help='JSON file containing instructions')
    parser.add_argument('--mode', choices=['text_infer_v3', 'text_infer_xml'],
                       default='text_infer_v3',
                       help='Execution mode (default: text_infer_v3)')

    args = parser.parse_args()
    json_file = args.json_file
    mode = args.mode

    logger.info(f"Using mode: {mode}")
    
    try:
        # Load instructions from JSON file
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # Extract instructions from the JSON
        instructions = []
        if isinstance(data, dict) and "instructions" in data:
            instructions = data["instructions"]
        elif isinstance(data, list):
            instructions = data
        else:
            # Try to find instructions in the JSON structure
            for key, value in data.items():
                if key == "instruction" or key == "instructions":
                    if isinstance(value, list):
                        instructions = value
                    else:
                        instructions = [value]
                    break

        if not instructions:
            logger.error("No instructions found in the JSON file")
            sys.exit(1)

        # Prepare results list
        results = []

        # Execute each instruction
        for i, instruction in enumerate(instructions):
            logger.info(f"Step {i+1}/{len(instructions)}")
            if isinstance(instruction, dict) and "instruction" in instruction:
                instruction_text = instruction["instruction"]
            elif isinstance(instruction, dict) and "text" in instruction:
                instruction_text = instruction["text"]
            elif isinstance(instruction, str):
                instruction_text = instruction
            else:
                logger.warning(f"Skipping invalid instruction format: {instruction}")
                continue

            response = execute_http_request(instruction_text, mode)

            # Record result
            result_entry = {
                "instruction": instruction_text,
                "step": i+1,
                "success": response["success"],
                "execution_time": response["execution_time"]
            }

            # Add ID if available
            if isinstance(instruction, dict) and "id" in instruction:
                result_entry["id"] = instruction["id"]

            # Add status code if available
            if "status_code" in response:
                result_entry["status_code"] = response["status_code"]

            if response["success"]:
                result_entry["response"] = response["response"]
            else:
                result_entry["error"] = response["error"]

            results.append(result_entry)

            # Optional: add delay between requests
            if i < len(instructions) - 1:
                time.sleep(2)  # 2 second delay between requests
        
        # Write results to JSON file
        output_file = f"results_{int(time.time())}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump({"results": results}, f, ensure_ascii=False, indent=2)
            
        logger.info(f"All instructions executed. Results saved to {output_file}")
        
    except FileNotFoundError:
        logger.error(f"File not found: {json_file}")
        sys.exit(1)
    except json.JSONDecodeError:
        logger.error(f"Invalid JSON format in file: {json_file}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"An error occurred: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
