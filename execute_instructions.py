#!/usr/bin/env python3
import json
import subprocess
import sys
import time
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def execute_curl_command(instruction):
    """Execute a curl command with the given instruction."""
    start_time = time.time()
    url = "http://mmfinderdrsandboxfdsvr.production.polaris/v1/agent/run_headless"
    headers = {"Content-Type": "application/json"}
    data = {
        "app_id": "wx25f982a55e60a540",
        "run_mode": "text_infer_v3",
        "from_username": "wxid_adldhsbu7ip312",
        "request_id": f"test_session_id_{int(time.time())}",
        "headless_mode": "2",
        "uin": "3194644195",
        "username": "wxid_adldhsbu7ip312",
        "instruction": instruction
    }
    
    # Convert data to JSON string
    data_json = json.dumps(data)
    
    # Construct curl command
    curl_cmd = [
        "curl", "--location", url,
        "--header", "Content-Type: application/json",
        "--data", data_json
    ]
    
    logger.info(f"Executing instruction: {instruction}")
    
    try:
        # Execute the curl command
        result = subprocess.run(curl_cmd, capture_output=True, text=True)
        execution_time = time.time() - start_time
        
        if result.returncode == 0:
            logger.info("Command executed successfully")
            logger.info(f"Response: {result.stdout[:200]}...")  # Show first 200 chars
            return {
                "success": True,
                "response": result.stdout,
                "execution_time": execution_time
            }
        else:
            logger.error(f"Command failed with error: {result.stderr}")
            return {
                "success": False,
                "error": result.stderr,
                "execution_time": execution_time
            }
    except Exception as e:
        execution_time = time.time() - start_time
        logger.error(f"Exception occurred: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "execution_time": execution_time
        }

def main():
    if len(sys.argv) < 2:
        logger.error("Usage: python execute_instructions.py merged_result.json")
        sys.exit(1)
    
    json_file = sys.argv[1]
    
    try:
        # Load instructions from JSON file
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Extract instructions from the JSON
        instructions = []
        if isinstance(data, dict) and "instructions" in data:
            instructions = data["instructions"]
        elif isinstance(data, list):
            instructions = data
        else:
            # Try to find instructions in the JSON structure
            for key, value in data.items():
                if key == "instruction" or key == "instructions":
                    if isinstance(value, list):
                        instructions = value
                    else:
                        instructions = [value]
                    break
        
        if not instructions:
            logger.error("No instructions found in the JSON file")
            sys.exit(1)
        
        # Prepare results list
        results = []
        
        # Execute each instruction
        for i, instruction in enumerate(instructions):
            logger.info(f"Step {i+1}/{len(instructions)}")
            if isinstance(instruction, dict) and "text" in instruction:
                instruction_text = instruction["text"]
            elif isinstance(instruction, str):
                instruction_text = instruction
            else:
                logger.warning(f"Skipping invalid instruction format: {instruction}")
                continue
                
            response = execute_curl_command(instruction_text)
            
            # Record result
            result_entry = {
                "instruction": instruction_text,
                "step": i+1,
                "success": response["success"],
                "execution_time": response["execution_time"]
            }
            
            if response["success"]:
                result_entry["response"] = response["response"]
            else:
                result_entry["error"] = response["error"]
                
            results.append(result_entry)
            
            # Optional: add delay between requests
            if i < len(instructions) - 1:
                time.sleep(2)  # 2 second delay between requests
        
        # Write results to JSON file
        output_file = f"results_{int(time.time())}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump({"results": results}, f, ensure_ascii=False, indent=2)
            
        logger.info(f"All instructions executed. Results saved to {output_file}")
        
    except FileNotFoundError:
        logger.error(f"File not found: {json_file}")
        sys.exit(1)
    except json.JSONDecodeError:
        logger.error(f"Invalid JSON format in file: {json_file}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"An error occurred: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
