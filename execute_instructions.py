#!/usr/bin/env python3
import json
import sys
import time
import logging
import requests

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def execute_http_request(instruction):
    """Execute an HTTP request with the given instruction."""
    start_time = time.time()
    url = "http://mmfinderdrsandboxfdsvr.production.polaris/v1/agent/run_headless"
    headers = {"Content-Type": "application/json"}
    data = {
        "app_id": "wx25f982a55e60a540",
        "run_mode": "text_infer_v3",
        "from_username": "wxid_adldhsbu7ip312",
        "request_id": f"test_session_id_{int(time.time())}",
        "headless_mode": "2",
        "uin": "3194644195",
        "username": "wxid_adldhsbu7ip312",
        "instruction": instruction
    }

    logger.info(f"Executing instruction: {instruction}")

    try:
        # Execute the HTTP request using requests
        response = requests.post(
            url,
            json=data,
            headers=headers,
            timeout=3600  # 1 hour timeout
        )
        execution_time = time.time() - start_time

        # Check if request was successful
        response.raise_for_status()

        response_text = response.text
        logger.info("Request executed successfully")
        logger.info(f"Response: {response_text[:200]}...")  # Show first 200 chars

        return {
            "success": True,
            "response": response_text,
            "execution_time": execution_time,
            "status_code": response.status_code
        }

    except requests.exceptions.Timeout:
        execution_time = time.time() - start_time
        error_msg = "Request timed out after 1 hour"
        logger.error(error_msg)
        return {
            "success": False,
            "error": error_msg,
            "execution_time": execution_time
        }
    except requests.exceptions.ConnectionError:
        execution_time = time.time() - start_time
        error_msg = "Connection error - unable to reach the server"
        logger.error(error_msg)
        return {
            "success": False,
            "error": error_msg,
            "execution_time": execution_time
        }
    except requests.exceptions.HTTPError as e:
        execution_time = time.time() - start_time
        error_msg = f"HTTP error {e.response.status_code}: {e.response.text}"
        logger.error(error_msg)
        return {
            "success": False,
            "error": error_msg,
            "execution_time": execution_time,
            "status_code": e.response.status_code
        }
    except Exception as e:
        execution_time = time.time() - start_time
        error_msg = f"Unexpected error: {str(e)}"
        logger.error(error_msg)
        return {
            "success": False,
            "error": error_msg,
            "execution_time": execution_time
        }

def main():
    if len(sys.argv) < 2:
        logger.error("Usage: python execute_instructions.py merged_result.json")
        sys.exit(1)
    
    json_file = sys.argv[1]
    
    try:
        # Load instructions from JSON file
        with open(json_file, 'r', encoding='utf-8') as f:
            instructions = json.load(f)
        
        # Prepare results list
        results = []
        
        # Execute each instruction
        for i, instruction in enumerate(instructions):
            logger.info(f"Executing instruction {i+1}/{len(instructions)}")
            if isinstance(instruction, dict) and "instruction" in instruction:
                instruction_text = instruction["instruction"]
            else:
                logger.warning(f"Skipping invalid instruction format: {instruction}")
                continue
                
            response = execute_http_request(instruction_text)
            
            # Record result
            result_entry = {
                "instruction": instruction_text,
                "id": instruction.get("id"),
                "execution_time": response["execution_time"],
                "status_code": response["status_code"]
            }
            
            if response.get("response"):
                result_entry["response"] = response["response"]
            
            if response.get("error"):
                result_entry["error"] = response["error"]
                
            results.append(result_entry)
            break
            
            # Optional: add delay between requests
            if i < len(instructions) - 1:
                time.sleep(0.5)  # 0.5 second delay between requests
        
        # Write results to JSON file
        output_file = f"results_{int(time.time())}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump({"results": results}, f, ensure_ascii=False, indent=2)
            
        logger.info(f"All instructions executed. Results saved to {output_file}")
        
    except FileNotFoundError:
        logger.error(f"File not found: {json_file}")
        sys.exit(1)
    except json.JSONDecodeError:
        logger.error(f"Invalid JSON format in file: {json_file}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"An error occurred: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
